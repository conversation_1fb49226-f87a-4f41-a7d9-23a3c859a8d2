
// THIS FILE IS AUTOGENERATED WHEN PAGES ARE UPDATED
import { lazy } from "react";
import { RouteObject } from "react-router";
import { SuspenseWrapper } from "./components/SuspenseWrapper";

const App = lazy(() => import("./pages/App.tsx"));
const Dashboard = lazy(() => import("./pages/Dashboard.tsx"));
const Auth = lazy(() => import("./pages/Auth.tsx"));
const Profile = lazy(() => import("./pages/Profile.tsx"));
// Keep the old imports for backward compatibility
const Login = lazy(() => import("./pages/Login.tsx"));
const Signup = lazy(() => import("./pages/Signup.tsx"));

export const userRoutes: RouteObject[] = [
	{ path: "/", element: <SuspenseWrapper><App /></SuspenseWrapper>},
	{ path: "/dashboard", element: <SuspenseWrapper><Dashboard /></SuspenseWrapper>},
	{ path: "/profile", element: <SuspenseWrapper><Profile /></SuspenseWrapper>},

	// New auth routes using the combined Auth component
	{ path: "/login", element: <SuspenseWrapper><Auth /></SuspenseWrapper>},
	{ path: "/signup", element: <SuspenseWrapper><Auth /></SuspenseWrapper>},
	{ path: "/auth", element: <SuspenseWrapper><Auth /></SuspenseWrapper>},
];
