/* Google Sign-In Button */
.googleSigninButton {
  position: relative;
  width: 100%;
  height: 50px;
  border-radius: 15px;
  border: 1px solid #dadce0;
  background-color: #fff;
  cursor: pointer;
  overflow: hidden;
  padding: 0;
  margin: 10px 0;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.googleSigninButton:hover {
  box-shadow: 0 2px 6px rgba(66, 133, 244, 0.3);
  border-color: #d2e3fc;
}

.googleSigninButton:active {
  background-color: #f5f5f5;
}

.googleSigninButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.googleSigninButtonContent {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.googleSigninButtonBg {
  position: absolute;
  top: 0;
  left: -100%;
  width: 300%;
  height: 100%;
  background: linear-gradient(to right, #003366, #004080, #0059b3, #0073e6);
  border-radius: 15px;
  transition: all 0.4s ease;
  opacity: 0;
  z-index: 1;
}

.googleSigninButton:hover .googleSigninButtonBg {
  opacity: 0.05;
  left: 0;
}

.googleIconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 12px;
}

.googleIcon {
  width: 24px;
  height: 24px;
}

.googleButtonText {
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #3c4043;
  letter-spacing: 0.25px;
}

/* Responsive styles */
@media (max-width: 430px) {
  .googleSigninButton {
    height: 45px;
  }
  
  .googleButtonText {
    font-size: 14px;
  }
  
  .googleIcon {
    width: 20px;
    height: 20px;
  }
}
