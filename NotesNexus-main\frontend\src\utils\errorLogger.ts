/**
 * Utility function for comprehensive error logging
 * @param context - The context where the error occurred (e.g., component name or function)
 * @param error - The error object
 * @returns A user-friendly error message
 */
export const logError = (context: string, error: any): string => {
  console.error(`[${context}] Error:`, error);
  
  // Log additional details if available
  if (error.response) {
    console.error('Response data:', error.response.data);
    console.error('Response status:', error.response.status);
  } else if (error.request) {
    console.error('Request made but no response received');
  }
  
  // Log Supabase specific error details if available
  if (error.code) {
    console.error('Error code:', error.code);
  }
  if (error.details) {
    console.error('Error details:', error.details);
  }
  if (error.hint) {
    console.error('Error hint:', error.hint);
  }
  
  // You could also send errors to a monitoring service here
  
  return error.message || 'An unknown error occurred';
};
