import React, { useState, useCallback, useEffect } from 'react';
import { supabase } from 'utils/supabaseClient';
import { useAuthStore } from 'utils/authStore';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress'; // For upload progress
import { toast } from 'sonner';
import { UploadCloud, AlertCircle } from 'lucide-react';
import { logError } from '@/utils/errorLogger';

interface Props {
    bucketName: string; // e.g., 'project_assets'
    // Optional path prefix, e.g., `${projectId}/`
    // If provided, ensures files are uploaded within this folder
    pathPrefix?: string;
    onUploadSuccess?: (filePath: string) => void; // Callback with the uploaded file path
    allowedFileTypes?: string[]; // e.g., ['image/png', 'image/jpeg']
    maxFileSize?: number; // Max file size in bytes
}

export function FileUpload({
    bucketName,
    pathPrefix = '',
    onUploadSuccess,
    allowedFileTypes,
    maxFileSize = 5 * 1024 * 1024 // Default 5MB
}: Props) {
    const { user } = useAuthStore();
    const [uploading, setUploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState<number | null>(null);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [fileError, setFileError] = useState<string | null>(null);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        setFileError(null); // Clear previous errors
        setSelectedFile(null);
        setUploadProgress(null);

        if (file) {
            // Validate file type
            if (allowedFileTypes && !allowedFileTypes.includes(file.type)) {
                setFileError(`Invalid file type. Allowed types: ${allowedFileTypes.join(', ')}`);
                return;
            }
            // Validate file size
            if (file.size > maxFileSize) {
                 setFileError(`File is too large. Max size: ${Math.round(maxFileSize / 1024 / 1024)}MB`);
                 return;
            }
            setSelectedFile(file);
        }
    };

    // Check if bucket exists
    const [bucketExists, setBucketExists] = useState<boolean | null>(null);

    useEffect(() => {
        const checkBucket = async () => {
            try {
                console.log(`Checking if bucket '${bucketName}' exists...`);
                const { data: buckets, error } = await supabase.storage.listBuckets();

                if (error) {
                    logError('FileUpload.checkBucket', error);
                    return;
                }

                const exists = buckets?.some(bucket => bucket.name === bucketName);
                setBucketExists(exists);

                if (!exists) {
                    console.warn(`Bucket '${bucketName}' does not exist!`);
                    setFileError(`Storage bucket '${bucketName}' does not exist. Please contact support.`);
                }
            } catch (error: any) {
                logError('FileUpload.checkBucket', error);
            }
        };

        if (bucketName) {
            checkBucket();
        }
    }, [bucketName]);

    const handleUpload = useCallback(async () => {
        if (!selectedFile || !user) {
            toast.error("No file selected or user not logged in.");
            return;
        }

        if (bucketExists === false) {
            toast.error(`Storage bucket '${bucketName}' does not exist. Please contact support.`);
            return;
        }

        setUploading(true);
        setUploadProgress(0);
        setFileError(null);

        // Construct the storage path: <user_id>/<pathPrefix><filename>
        // Ensure pathPrefix ends with '/' if provided and not empty
        const prefix = pathPrefix && pathPrefix.trim() !== ''
                       ? (pathPrefix.endsWith('/') ? pathPrefix : `${pathPrefix}/`)
                       : '';
        const filePath = `${user.id}/${prefix}${selectedFile.name}`;

        console.log(`Starting upload to Supabase bucket: ${bucketName}, path: ${filePath}`);

        try {
            // Simulate progress for better UX
            const progressInterval = setInterval(() => {
                setUploadProgress(prev => {
                    if (prev === null) return 10;
                    return Math.min(prev + 10, 90); // Max 90% until confirmed
                });
            }, 300);

            // Network request monitoring
            console.log('Sending upload request to Supabase...');

            const { data, error } = await supabase.storage
                .from(bucketName)
                .upload(filePath, selectedFile, {
                    cacheControl: '3600', // Optional: Cache control
                    upsert: true, // Optional: Overwrite file if it exists
                });

            clearInterval(progressInterval);
            console.log('Upload request completed. Checking response...');

            if (error) {
                logError('FileUpload.handleUpload', error);
                throw error;
            }

            // Verify the upload by trying to get the file URL
            const { data: urlData, error: urlError } = await supabase.storage
                .from(bucketName)
                .createSignedUrl(filePath, 60);

            if (urlError) {
                logError('FileUpload.verifyUpload', urlError);
                throw new Error('File uploaded but could not verify. Please check your files.');
            }

            if (!urlData || !urlData.signedUrl) {
                throw new Error('File may not have uploaded correctly. Could not generate URL.');
            }

            // Only set to 100% after confirmed success
            setUploadProgress(100);
            console.log('File uploaded successfully:', filePath);
            console.log('File URL:', urlData.signedUrl);

            toast.success(`File "${selectedFile.name}" uploaded successfully!`);
            setSelectedFile(null); // Clear selection after upload

            if (onUploadSuccess) {
                onUploadSuccess(filePath); // Pass the full path
            }

        } catch (error: any) {
            setUploadProgress(0); // Reset progress on error
            const errorMessage = logError('FileUpload.handleUpload', error);
            setFileError(`Upload failed: ${errorMessage}`);
            toast.error(`Upload failed: ${errorMessage}`);
        } finally {
            setUploading(false);
        }
    }, [selectedFile, user, bucketName, pathPrefix, onUploadSuccess, bucketExists]);

    return (
        <div className="space-y-4 p-4 border rounded-lg bg-card">
            <Label htmlFor="file-upload" className="text-lg font-semibold">Upload File</Label>

            {/* Bucket status indicator */}
            {bucketExists === false && (
                <div className="flex items-center p-2 bg-red-50 text-red-700 rounded border border-red-200 text-sm">
                    <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>Storage bucket not found. Please contact support.</span>
                </div>
            )}

            <Input
                id="file-upload"
                type="file"
                onChange={handleFileChange}
                disabled={uploading || bucketExists === false}
                className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
            />

            {selectedFile && (
                <p className="text-sm text-muted-foreground">
                    Selected: {selectedFile.name} ({Math.round(selectedFile.size / 1024)} KB)
                </p>
            )}

            {fileError && (
                <p className="text-sm text-red-600">{fileError}</p>
            )}

            {uploadProgress !== null && uploadProgress > 0 && (
                <div className="space-y-1">
                    <Progress value={uploadProgress} className="w-full" />
                    <p className="text-xs text-muted-foreground text-right">{uploadProgress}%</p>
                </div>
            )}

            <Button
                onClick={handleUpload}
                disabled={!selectedFile || uploading || !!fileError || bucketExists === false}
            >
                <UploadCloud className="mr-2 h-4 w-4" />
                {uploading ? `Uploading... ${uploadProgress !== null ? `${uploadProgress}%` : ''}` : 'Upload'}
            </Button>

            {/* Debug info in development */}
            {import.meta.env.DEV && (
                <div className="mt-4 p-2 bg-gray-100 rounded text-xs text-gray-600">
                    <p>Debug Info:</p>
                    <p>Bucket: {bucketName} (Exists: {bucketExists === null ? 'checking...' : bucketExists ? 'yes' : 'no'})</p>
                    <p>User ID: {user?.id || 'not logged in'}</p>
                </div>
            )}
        </div>
    );
}
