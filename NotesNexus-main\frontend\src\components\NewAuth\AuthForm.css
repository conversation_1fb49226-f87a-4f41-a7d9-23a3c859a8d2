@import url('https://fonts.googleapis.com/css?family=Poppins:400,500,600,700&display=swap');

/* Base styles */
.auth-wrapper {
  overflow: hidden;
  max-width: 390px;
  width: 100%;
  background: #fff;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0px 15px 20px rgba(0,0,0,0.1);
  margin: 0 auto;
}

/* Title section */
.auth-wrapper .title-text {
  display: flex;
  width: 200%;
}

.auth-wrapper .title {
  width: 50%;
  font-size: 35px;
  font-weight: 600;
  text-align: center;
  transition: all 0.6s cubic-bezier(0.68,-0.55,0.265,1.55);
}

/* Slide controls */
.auth-wrapper .slide-controls {
  position: relative;
  display: flex;
  height: 50px;
  width: 100%;
  overflow: hidden;
  margin: 30px 0 10px 0;
  justify-content: space-between;
  border: 1px solid lightgrey;
  border-radius: 15px;
}

.slide-controls .slide {
  height: 100%;
  width: 100%;
  color: #000;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  line-height: 48px;
  cursor: pointer;
  z-index: 1;
  transition: all 0.6s ease;
}

.slide-controls .slide.login {
  color: #fff;
}

.slide-controls .slider-tab {
  position: absolute;
  height: 100%;
  width: 50%;
  left: 0;
  z-index: 0;
  border-radius: 15px;
  background: linear-gradient(to right, #003366, #004080, #0059b3, #0073e6);
  transition: all 0.6s cubic-bezier(0.68,-0.55,0.265,1.55);
}

input[type="radio"] {
  display: none;
}

#signup:checked ~ .slider-tab {
  left: 50%;
}

#signup:checked ~ label.signup {
  color: #fff;
  cursor: default;
  user-select: none;
}

#signup:checked ~ label.login {
  color: #000;
}

#login:checked ~ label.signup {
  color: #000;
}

#login:checked ~ label.login {
  color: #fff;
  cursor: default;
  user-select: none;
}

/* Form container */
.auth-wrapper .form-container {
  width: 100%;
  overflow: hidden;
}

.form-container .form-inner {
  display: flex;
  width: 200%;
}

.form-container .form-inner form {
  width: 50%;
  transition: all 0.6s cubic-bezier(0.68,-0.55,0.265,1.55);
}

/* Form fields */
.form-inner form .field {
  height: 50px;
  width: 100%;
  margin-top: 20px;
}

.form-inner form .field input {
  height: 100%;
  width: 100%;
  outline: none;
  padding-left: 15px;
  border-radius: 15px;
  border: 1px solid lightgrey;
  border-bottom-width: 2px;
  font-size: 17px;
  transition: all 0.3s ease;
}

.form-inner form .field input:focus {
  border-color: #1a75ff;
}

.form-inner form .field input::placeholder {
  color: #999;
  transition: all 0.3s ease;
}

form .field input:focus::placeholder {
  color: #1a75ff;
}

/* Links */
.form-inner form .pass-link {
  margin-top: 5px;
}

.form-inner form .signup-link {
  text-align: center;
  margin-top: 30px;
}

.form-inner form .pass-link a,
.form-inner form .signup-link a {
  color: #1a75ff;
  text-decoration: none;
}

.form-inner form .pass-link a:hover,
.form-inner form .signup-link a:hover {
  text-decoration: underline;
}

/* Button styles */
form .btn {
  height: 50px;
  width: 100%;
  border-radius: 15px;
  position: relative;
  overflow: hidden;
  margin-top: 20px;
}

form .btn .btn-layer {
  height: 100%;
  width: 300%;
  position: absolute;
  left: -100%;
  background: linear-gradient(to right, #003366, #004080, #0059b3, #0073e6);
  border-radius: 15px;
  transition: all 0.4s ease;
}

form .btn:hover .btn-layer {
  left: 0;
}

form .btn input[type="submit"] {
  height: 100%;
  width: 100%;
  z-index: 1;
  position: relative;
  background: none;
  border: none;
  color: #fff;
  padding-left: 0;
  border-radius: 15px;
  font-size: 20px;
  font-weight: 500;
  cursor: pointer;
}

form .btn input[type="submit"]:disabled {
  cursor: not-allowed;
  opacity: 0.8;
}

/* Success message */
.success-message {
  width: 100%;
  text-align: center;
  padding: 20px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 15px;
  margin: 20px 0;
}

.success-message p {
  margin-bottom: 10px;
  color: #0073e6;
}

.back-to-login {
  background: linear-gradient(to right, #003366, #004080, #0059b3, #0073e6);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 15px;
  margin-top: 15px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
}

/* Google auth section */
.google-auth {
  margin-top: 20px;
}

.divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin-bottom: 15px;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #ddd;
}

.divider span {
  padding: 0 10px;
  color: #777;
  font-size: 14px;
}

/* Responsive styles */
@media (max-width: 430px) {
  .auth-wrapper {
    padding: 25px 15px;
  }

  .auth-wrapper .title {
    font-size: 30px;
  }

  .slide-controls .slide {
    font-size: 16px;
  }

  form .btn input[type="submit"] {
    font-size: 18px;
  }
}
