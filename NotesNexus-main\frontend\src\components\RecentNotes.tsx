import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Search, Filter, Clock, Star, Download, Eye, ThumbsUp, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/utils/supabaseClient';

// Note interface
interface Note {
  id: string;
  title: string;
  subject: string;
  level: string;
  grade: string;
  author: string;
  upload_date: string; // Changed from uploadDate to match Supabase naming convention
  views: number;
  downloads: number;
  likes: number;
  rating: number;
  tags: string[];
}

// Filter options
const subjects = ['All Subjects', 'Mathematics', 'Nepali', 'English', 'Science', 'Computer Science', 'History', 'Biology'];
const levels = ['All Levels', 'Primary Level', 'Lower Secondary Level', 'Secondary Level', 'Higher Secondary Level', 'Bachelor\'s Degree Programs', 'Master\'s Degree Programs'];
const sortOptions = ['Most Recent', 'Most Popular', 'Highest Rated', 'Most Downloaded'];

export function RecentNotes() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('All Subjects');
  const [selectedLevel, setSelectedLevel] = useState('All Levels');
  const [sortBy, setSortBy] = useState('Most Recent');
  const [showFilters, setShowFilters] = useState(false);
  const [notes, setNotes] = useState<Note[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch notes from Supabase
  useEffect(() => {
    async function fetchNotes() {
      try {
        setLoading(true);

        // Fetch notes from the 'notes' table in Supabase
        const { data, error } = await supabase
          .from('notes')
          .select('*');

        if (error) {
          throw error;
        }

        if (data) {
          // Transform data if needed (e.g., parse tags from JSON string if stored that way)
          const formattedNotes = data.map((note: any) => ({
            ...note,
            tags: Array.isArray(note.tags) ? note.tags : JSON.parse(note.tags || '[]')
          }));

          setNotes(formattedNotes);
        }
      } catch (err: any) {
        console.error('Error fetching notes:', err);
        setError(err.message || 'Failed to fetch notes');
      } finally {
        setLoading(false);
      }
    }

    fetchNotes();
  }, []);

  // Filter and sort notes based on user selections
  const filteredNotes = notes
    .filter(note => {
      const matchesSearch = note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           note.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           note.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesSubject = selectedSubject === 'All Subjects' || note.subject === selectedSubject;
      const matchesLevel = selectedLevel === 'All Levels' || note.level === selectedLevel;

      return matchesSearch && matchesSubject && matchesLevel;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'Most Recent':
          return new Date(b.upload_date).getTime() - new Date(a.upload_date).getTime();
        case 'Most Popular':
          return b.views - a.views;
        case 'Highest Rated':
          return b.rating - a.rating;
        case 'Most Downloaded':
          return b.downloads - a.downloads;
        default:
          return 0;
      }
    });

  return (
    <div className="w-full max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-8 text-white">Recent Notes</h2>
      <p className="text-center text-gray-300 mb-10">
        Discover the latest study materials uploaded by students across Nepal
      </p>

      {/* Search and Filter Bar */}
      <div className="mb-8 space-y-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              type="text"
              placeholder="Search notes by title, subject, or tags..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-gray-800 border-gray-700 text-white"
            />
          </div>
          <Button
            variant="outline"
            className="flex items-center gap-2 border-gray-700 text-gray-300"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter size={18} />
            Filters
          </Button>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[180px] bg-gray-800 border-gray-700 text-white">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-700 text-white">
              {sortOptions.map(option => (
                <SelectItem key={option} value={option}>{option}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Expandable Filters */}
        {showFilters && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-800 rounded-lg border border-gray-700"
          >
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Subject</label>
              <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select Subject" />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600 text-white">
                  {subjects.map(subject => (
                    <SelectItem key={subject} value={subject}>{subject}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Academic Level</label>
              <Select value={selectedLevel} onValueChange={setSelectedLevel}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select Level" />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600 text-white">
                  {levels.map(level => (
                    <SelectItem key={level} value={level}>{level}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </motion.div>
        )}
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex flex-col items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-4" />
          <p className="text-gray-400">Loading notes...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="text-center py-12 bg-red-900/20 rounded-lg border border-red-700">
          <h3 className="text-xl font-semibold text-white mb-2">Error loading notes</h3>
          <p className="text-gray-400 mb-6">{error}</p>
          <Button
            variant="outline"
            className="border-gray-600 text-gray-300"
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      )}

      {/* Notes Grid */}
      {!loading && !error && filteredNotes.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredNotes.map((note) => (
            <motion.div
              key={note.id}
              className="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700 hover:border-gray-500 transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="p-5">
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-xl font-semibold text-white">{note.title}</h3>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-400 mr-1" />
                    <span className="text-white">{note.rating}</span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 mb-3">
                  <Badge variant="outline" className="bg-blue-900/30 text-blue-300 border-blue-700">
                    {note.subject}
                  </Badge>
                  <Badge variant="outline" className="bg-purple-900/30 text-purple-300 border-purple-700">
                    {note.level}
                  </Badge>
                  <Badge variant="outline" className="bg-green-900/30 text-green-300 border-green-700">
                    {note.grade}
                  </Badge>
                </div>

                <div className="flex items-center text-gray-400 text-sm mb-4">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>Uploaded on {new Date(note.upload_date).toLocaleDateString()}</span>
                  <span className="mx-2">•</span>
                  <span>by {note.author}</span>
                </div>

                <div className="flex flex-wrap gap-1 mb-4">
                  {note.tags.map(tag => (
                    <span key={tag} className="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">
                      #{tag}
                    </span>
                  ))}
                </div>

                <div className="flex justify-between items-center pt-3 border-t border-gray-700">
                  <div className="flex items-center space-x-4 text-sm text-gray-400">
                    <div className="flex items-center">
                      <Eye className="h-4 w-4 mr-1" />
                      <span>{note.views}</span>
                    </div>
                    <div className="flex items-center">
                      <Download className="h-4 w-4 mr-1" />
                      <span>{note.downloads}</span>
                    </div>
                    <div className="flex items-center">
                      <ThumbsUp className="h-4 w-4 mr-1" />
                      <span>{note.likes}</span>
                    </div>
                  </div>
                  <Button size="sm" variant="default" className="bg-blue-600 hover:bg-blue-700">
                    View Note
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : !loading && !error ? (
        <div className="text-center py-12 bg-gray-800 rounded-lg border border-gray-700">
          <h3 className="text-xl font-semibold text-white mb-2">No notes found</h3>
          <p className="text-gray-400 mb-6">Try adjusting your search or filter criteria</p>
          <Button
            variant="outline"
            className="border-gray-600 text-gray-300"
            onClick={() => {
              setSearchQuery('');
              setSelectedSubject('All Subjects');
              setSelectedLevel('All Levels');
              setSortBy('Most Recent');
            }}
          >
            Clear Filters
          </Button>
        </div>
      ) : null}
    </div>
  );
}
