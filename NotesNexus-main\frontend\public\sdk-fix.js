// SDK Fix for Chrome Extension
(function() {
  // Store the original appendChild method
  const originalAppendChild = Document.prototype.appendChild;
  const originalDocumentElementAppendChild = HTMLHtmlElement.prototype.appendChild;

  // Override the appendChild method for document.documentElement
  HTMLHtmlElement.prototype.appendChild = function(element) {
    try {
      // Try the original method first
      return originalDocumentElementAppendChild.call(this, element);
    } catch (error) {
      console.warn('Chrome extension appendChild error intercepted:', error);
      
      // Alternative: append to body instead of documentElement
      if (document.body) {
        return document.body.appendChild(element);
      } else {
        // If body is not available yet, use a MutationObserver to wait for it
        const observer = new MutationObserver(function(mutations) {
          if (document.body) {
            document.body.appendChild(element);
            observer.disconnect();
          }
        });
        
        observer.observe(document, { childList: true, subtree: true });
        return element; // Return the element to maintain API compatibility
      }
    }
  };

  // Also override document.appendChild for completeness
  Document.prototype.appendChild = function(element) {
    try {
      return originalAppendChild.call(this, element);
    } catch (error) {
      console.warn('Chrome extension document.appendChild error intercepted:', error);
      
      // Alternative: append to body instead
      if (document.body) {
        return document.body.appendChild(element);
      } else {
        // If body is not available yet, use a MutationObserver to wait for it
        const observer = new MutationObserver(function(mutations) {
          if (document.body) {
            document.body.appendChild(element);
            observer.disconnect();
          }
        });
        
        observer.observe(document, { childList: true, subtree: true });
        return element; // Return the element to maintain API compatibility
      }
    }
  };

  console.log('SDK fix for Chrome extension loaded successfully');
})();
