@import url('https://fonts.googleapis.com/css?family=Poppins:400,500,600,700&display=swap');

.authFormWrapper {
  overflow: hidden;
  max-width: 390px;
  width: 100%;
  background: #fff;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0px 15px 20px rgba(0,0,0,0.1);
  margin: 0 auto;
}

.formTitle {
  font-size: 28px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.slideControls {
  position: relative;
  display: flex;
  height: 50px;
  width: 100%;
  overflow: hidden;
  margin: 30px 0 10px 0;
  justify-content: space-between;
  border: 1px solid lightgrey;
  border-radius: 15px;
}

.slide {
  height: 100%;
  width: 100%;
  color: #000;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  line-height: 48px;
  cursor: pointer;
  z-index: 1;
  transition: all 0.6s ease;
}

.sliderTab {
  position: absolute;
  height: 100%;
  width: 50%;
  left: 0;
  z-index: 0;
  border-radius: 15px;
  background: linear-gradient(to right, #003366, #004080, #0059b3, #0073e6);
  transition: all 0.6s cubic-bezier(0.68,-0.55,0.265,1.55);
}

.radioInput {
  display: none;
}

input[id="signup"]:checked ~ .sliderTab {
  left: 50%;
}

input[id="signup"]:checked ~ label.signup {
  color: #fff;
  cursor: default;
  user-select: none;
}

input[id="signup"]:checked ~ label.login {
  color: #000;
}

input[id="login"]:checked ~ label.signup {
  color: #000;
}

input[id="login"]:checked ~ label.login {
  color: #fff;
  cursor: default;
  user-select: none;
}

.formContainer {
  width: 100%;
  overflow: hidden;
}

.formInner {
  display: flex;
  width: 200%;
}

.formInner form {
  width: 50%;
  transition: all 0.6s cubic-bezier(0.68,-0.55,0.265,1.55);
}

/* Form transitions are handled by JavaScript */
.formInner form.login {
  margin-left: 0%;
}

.formInner form.signup {
  margin-left: 0%;
}

.formInner form .field {
  height: 50px;
  width: 100%;
  margin-top: 20px;
}

.formInner form .field input {
  height: 100%;
  width: 100%;
  outline: none;
  padding-left: 15px;
  border-radius: 15px;
  border: 1px solid lightgrey;
  border-bottom-width: 2px;
  font-size: 17px;
  transition: all 0.3s ease;
}

.formInner form .field input:focus {
  border-color: #1a75ff;
}

.formInner form .field input::placeholder {
  color: #999;
  transition: all 0.3s ease;
}

.formInner form .field input:focus::placeholder {
  color: #1a75ff;
}

.formInner form .passLink {
  margin-top: 5px;
}

.formInner form .signupLink,
.formInner form .loginLink {
  text-align: center;
  margin-top: 30px;
}

.formInner form .passLink a,
.formInner form .signupLink a,
.formInner form .loginLink a {
  color: #1a75ff;
  text-decoration: none;
}

.formInner form .passLink a:hover,
.formInner form .signupLink a:hover,
.formInner form .loginLink a:hover {
  text-decoration: underline;
}

.btn {
  height: 50px;
  width: 100%;
  border-radius: 15px;
  position: relative;
  overflow: hidden;
  margin-top: 20px;
}

.btn .btnLayer {
  height: 100%;
  width: 300%;
  position: absolute;
  left: -100%;
  background: linear-gradient(to right, #003366, #004080, #0059b3, #0073e6);
  border-radius: 15px;
  transition: all 0.4s ease;
}

.btn:hover .btnLayer {
  left: 0;
}

.btn input[type="submit"] {
  height: 100%;
  width: 100%;
  z-index: 1;
  position: relative;
  background: none;
  border: none;
  color: #fff;
  padding-left: 0;
  border-radius: 15px;
  font-size: 20px;
  font-weight: 500;
  cursor: pointer;
}

.successMessage {
  width: 100%;
  text-align: center;
  padding: 20px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 15px;
  margin: 20px 0;
}

.successMessage p {
  margin-bottom: 10px;
  color: #0073e6;
}

.backToLogin {
  background: linear-gradient(to right, #003366, #004080, #0059b3, #0073e6);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 15px;
  margin-top: 15px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
}

.termsText {
  text-align: center;
  margin-top: 20px;
  font-size: 12px;
  color: #666;
}

.termsText a {
  color: #1a75ff;
  text-decoration: none;
}

.termsText a:hover {
  text-decoration: underline;
}

/* Loading skeleton */
.authFormLoading {
  width: 100%;
}

.loadingSkeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 15px;
  margin-bottom: 15px;
}

.loadingSkeleton.title {
  height: 40px;
  width: 70%;
  margin: 0 auto 30px;
}

.loadingSkeleton.field {
  height: 50px;
  width: 100%;
}

.loadingSkeleton.button {
  height: 50px;
  width: 100%;
  margin-top: 20px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Google Sign In Button */
.orDivider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 20px 0;
}

.orDivider::before,
.orDivider::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #ddd;
}

.orDivider span {
  padding: 0 10px;
  color: #777;
  font-size: 14px;
}

/* Responsive styles */
@media (max-width: 430px) {
  .authFormWrapper {
    padding: 25px 15px;
  }

  .formTitle {
    font-size: 24px;
  }

  .slide {
    font-size: 16px;
  }

  .btn input[type="submit"] {
    font-size: 18px;
  }
}
