// This script runs in the context of the web page
(function() {
  // Store the original appendChild method
  const originalAppendChild = HTMLHtmlElement.prototype.appendChild;
  
  // Override the appendChild method for document.documentElement
  HTMLHtmlElement.prototype.appendChild = function(element) {
    try {
      // Try the original method first
      return originalAppendChild.call(this, element);
    } catch (error) {
      console.warn('SDK appendChild error intercepted:', error);
      
      // Alternative: append to body instead of documentElement
      if (document.body) {
        return document.body.appendChild(element);
      } else {
        // If body is not available yet, use a MutationObserver to wait for it
        const observer = new MutationObserver(function(mutations) {
          if (document.body) {
            document.body.appendChild(element);
            observer.disconnect();
          }
        });
        
        observer.observe(document, { childList: true, subtree: true });
        return element; // Return the element to maintain API compatibility
      }
    }
  };
  
  console.log('Page script fix for SDK loaded');
})();
