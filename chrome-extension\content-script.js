// Content script for Chrome extension
(function() {
  // Fix for document.documentElement.appendChild
  const originalAppendChild = HTMLHtmlElement.prototype.appendChild;
  
  HTMLHtmlElement.prototype.appendChild = function(element) {
    try {
      return originalAppendChild.call(this, element);
    } catch (error) {
      console.warn('Chrome extension appendChild error intercepted:', error);
      
      // Fallback to body
      if (document.body) {
        return document.body.appendChild(element);
      }
      
      // If body isn't available, queue it up
      window.addEventListener('DOMContentLoaded', () => {
        document.body.appendChild(element);
      });
      
      return element;
    }
  };
  
  // Inject a script element that will run in the page context
  function injectScript(file) {
    const script = document.createElement('script');
    script.setAttribute('type', 'text/javascript');
    script.setAttribute('src', file);
    (document.head || document.documentElement).appendChild(script);
  }
  
  // Inject our fix script
  injectScript(chrome.runtime.getURL('page-script.js'));
  
  console.log('Chrome extension content script loaded');
})();
