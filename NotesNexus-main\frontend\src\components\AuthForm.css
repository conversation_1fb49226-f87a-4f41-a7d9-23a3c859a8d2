@import url('https://fonts.googleapis.com/css?family=Poppins:400,500,600,700&display=swap');

.auth-form-wrapper {
  overflow: hidden;
  max-width: 390px;
  width: 100%;
  background: #fff;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0px 15px 20px rgba(0,0,0,0.1);
  margin: 0 auto;
}

.auth-form-wrapper .form-title {
  font-size: 28px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.auth-form-wrapper .slide-controls {
  position: relative;
  display: flex;
  height: 50px;
  width: 100%;
  overflow: hidden;
  margin: 30px 0 10px 0;
  justify-content: space-between;
  border: 1px solid lightgrey;
  border-radius: 15px;
}

.slide-controls .slide {
  height: 100%;
  width: 100%;
  color: #000;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  line-height: 48px;
  cursor: pointer;
  z-index: 1;
  transition: all 0.6s ease;
}

.slide-controls .slider-tab {
  position: absolute;
  height: 100%;
  width: 50%;
  left: 0;
  z-index: 0;
  border-radius: 15px;
  background: linear-gradient(to right, #003366, #004080, #0059b3, #0073e6);
  transition: all 0.6s cubic-bezier(0.68,-0.55,0.265,1.55);
}

input[type="radio"] {
  display: none;
}

#signup:checked ~ .slider-tab {
  left: 50%;
}

#signup:checked ~ label.signup {
  color: #fff;
  cursor: default;
  user-select: none;
}

#signup:checked ~ label.login {
  color: #000;
}

#login:checked ~ label.signup {
  color: #000;
}

#login:checked ~ label.login {
  color: #fff;
  cursor: default;
  user-select: none;
}

.auth-form-wrapper .form-container {
  width: 100%;
  overflow: hidden;
}

.form-container .form-inner {
  display: flex;
  width: 200%;
}

.form-container .form-inner form {
  width: 50%;
  transition: all 0.6s cubic-bezier(0.68,-0.55,0.265,1.55);
}

/* Form transitions are now handled by JavaScript */
.form-inner form.login {
  margin-left: 0%;
}

.form-inner form.signup {
  margin-left: 0%;
}

.form-inner form .field {
  height: 50px;
  width: 100%;
  margin-top: 20px;
}

.form-inner form .field input {
  height: 100%;
  width: 100%;
  outline: none;
  padding-left: 15px;
  border-radius: 15px;
  border: 1px solid lightgrey;
  border-bottom-width: 2px;
  font-size: 17px;
  transition: all 0.3s ease;
}

.form-inner form .field input:focus {
  border-color: #1a75ff;
}

.form-inner form .field input::placeholder {
  color: #999;
  transition: all 0.3s ease;
}

form .field input:focus::placeholder {
  color: #1a75ff;
}

.form-inner form .pass-link {
  margin-top: 5px;
}

.form-inner form .signup-link,
.form-inner form .login-link {
  text-align: center;
  margin-top: 30px;
}

.form-inner form .pass-link a,
.form-inner form .signup-link a,
.form-inner form .login-link a {
  color: #1a75ff;
  text-decoration: none;
}

.form-inner form .pass-link a:hover,
.form-inner form .signup-link a:hover,
.form-inner form .login-link a:hover {
  text-decoration: underline;
}

form .btn {
  height: 50px;
  width: 100%;
  border-radius: 15px;
  position: relative;
  overflow: hidden;
  margin-top: 20px;
}

form .btn .btn-layer {
  height: 100%;
  width: 300%;
  position: absolute;
  left: -100%;
  background: linear-gradient(to right, #003366, #004080, #0059b3, #0073e6);
  border-radius: 15px;
  transition: all 0.4s ease;
}

form .btn:hover .btn-layer {
  left: 0;
}

form .btn input[type="submit"] {
  height: 100%;
  width: 100%;
  z-index: 1;
  position: relative;
  background: none;
  border: none;
  color: #fff;
  padding-left: 0;
  border-radius: 15px;
  font-size: 20px;
  font-weight: 500;
  cursor: pointer;
}

.success-message {
  width: 100%;
  text-align: center;
  padding: 20px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 15px;
  margin: 20px 0;
}

.success-message p {
  margin-bottom: 10px;
  color: #0073e6;
}

.success-message .back-to-login {
  background: linear-gradient(to right, #003366, #004080, #0059b3, #0073e6);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 15px;
  margin-top: 15px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
}

.terms-text {
  text-align: center;
  margin-top: 20px;
  font-size: 12px;
  color: #666;
}

.terms-text a {
  color: #1a75ff;
  text-decoration: none;
}

.terms-text a:hover {
  text-decoration: underline;
}

/* Loading skeleton */
.auth-form-loading {
  width: 100%;
}

.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 15px;
  margin-bottom: 15px;
}

.loading-skeleton.title {
  height: 40px;
  width: 70%;
  margin: 0 auto 30px;
}

.loading-skeleton.field {
  height: 50px;
  width: 100%;
}

.loading-skeleton.button {
  height: 50px;
  width: 100%;
  margin-top: 20px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Google Sign In Button */
.or-divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 20px 0;
}

.or-divider::before,
.or-divider::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #ddd;
}

.or-divider span {
  padding: 0 10px;
  color: #777;
  font-size: 14px;
}

.google-btn {
  width: 100%;
  height: 42px;
  background-color: #fff;
  border-radius: 15px;
  box-shadow: 0 3px 4px 0 rgba(0,0,0,.25);
  cursor: pointer;
  display: flex;
  align-items: center;
  margin: 10px 0;
  transition: all 0.2s ease;
}

.google-btn:hover {
  box-shadow: 0 0 6px #4285f4;
}

.google-btn:active {
  background: #f5f5f5;
}

.google-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 1px;
}

.google-icon {
  width: 18px;
  height: 18px;
}

.btn-text {
  color: #757575;
  font-size: 14px;
  letter-spacing: 0.2px;
  margin-left: 10px;
  font-family: 'Poppins', sans-serif;
}

/* Responsive styles */
@media (max-width: 430px) {
  .auth-form-wrapper {
    padding: 25px 15px;
  }

  .auth-form-wrapper .form-title {
    font-size: 24px;
  }

  .slide-controls .slide {
    font-size: 16px;
  }

  form .btn input[type="submit"] {
    font-size: 18px;
  }

  .google-btn {
    height: 38px;
  }

  .btn-text {
    font-size: 13px;
  }
}
