-- Create notes bucket if it doesn't exist
insert into storage.buckets (id, name)
values ('notes', 'notes')
on conflict do nothing;

-- Allow public access to view notes
create policy "Notes are publicly accessible"
on storage.objects for select
using ( bucket_id = 'notes' );

-- Allow authenticated users to upload notes
create policy "Users can upload notes"
on storage.objects for insert
with check (
    bucket_id = 'notes' 
    and auth.role() = 'authenticated'
);

-- Allow users to update their own notes
create policy "Users can update own notes"
on storage.objects for update
using (
    bucket_id = 'notes' 
    and auth.uid()::text = (storage.foldername(name))[1]
);

-- Allow users to delete their own notes
create policy "Users can delete own notes"
on storage.objects for delete
using (
    bucket_id = 'notes' 
    and auth.uid()::text = (storage.foldername(name))[1]
);
